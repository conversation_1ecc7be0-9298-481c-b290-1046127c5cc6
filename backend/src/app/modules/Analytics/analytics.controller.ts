import { Request, Response } from 'express';
import httpStatus from 'http-status';
import catchAsync from '../../utils/catchAsync';
import sendResponse from '../../utils/sendResponse';
import AppError from '../../errors/AppError';
import AnalyticsService from '../../services/analytics/AnalyticsService';
import ActivityTrackingService from '../../services/activity/ActivityTrackingService';
import { IAnalyticsQuery, IActivityQuery } from './analytics.interface';

const analyticsService = new AnalyticsService();
const activityTrackingService = new ActivityTrackingService();

/**
 * Get comprehensive teacher analytics
 */
const getTeacherAnalytics = catchAsync(async (req: Request, res: Response) => {
  const { teacherId } = req.params;
  const { 
    period = 'monthly',
    courseId,
    startDate,
    endDate,
    compareWithPrevious = false 
  } = req.query;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only access your own analytics');
  }

  const filters = {
    teacherId,
    courseId: courseId as string,
    period: period as 'daily' | 'weekly' | 'monthly' | 'yearly',
    startDate: startDate ? new Date(startDate as string) : undefined,
    endDate: endDate ? new Date(endDate as string) : undefined,
    compareWithPrevious: compareWithPrevious === 'true',
  };

  const analytics = await analyticsService.getTeacherAnalytics(filters);

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Teacher analytics retrieved successfully',
    data: analytics,
  });
});

/**
 * Get course-specific analytics
 */
const getCourseAnalytics = catchAsync(async (req: Request, res: Response) => {
  const { teacherId, courseId } = req.params;
  const { period = 'monthly', startDate, endDate } = req.query;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only access your own course analytics');
  }

  const dateRange = startDate && endDate ? {
    startDate: new Date(startDate as string),
    endDate: new Date(endDate as string),
  } : undefined;

  const analytics = await analyticsService.getCourseAnalytics(teacherId, courseId, dateRange);

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Course analytics retrieved successfully',
    data: analytics,
  });
});

/**
 * Get revenue analytics
 */
const getRevenueAnalytics = catchAsync(async (req: Request, res: Response) => {
  const { teacherId } = req.params;
  const { courseId, period = 'monthly', startDate, endDate } = req.query;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only access your own revenue analytics');
  }

  const dateRange = startDate && endDate ? {
    startDate: new Date(startDate as string),
    endDate: new Date(endDate as string),
  } : undefined;

  const analytics = await analyticsService.getRevenueAnalytics(
    teacherId,
    courseId as string,
    dateRange
  );

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Revenue analytics retrieved successfully',
    data: analytics,
  });
});

/**
 * Get performance metrics
 */
const getPerformanceMetrics = catchAsync(async (req: Request, res: Response) => {
  const { teacherId } = req.params;
  const { courseId, period = 'monthly', startDate, endDate } = req.query;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only access your own performance metrics');
  }

  const dateRange = startDate && endDate ? {
    startDate: new Date(startDate as string),
    endDate: new Date(endDate as string),
  } : undefined;

  const metrics = await analyticsService.getPerformanceMetrics(
    teacherId,
    courseId as string,
    dateRange
  );

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Performance metrics retrieved successfully',
    data: metrics,
  });
});

/**
 * Get student engagement analytics
 */
const getStudentEngagement = catchAsync(async (req: Request, res: Response) => {
  const { teacherId } = req.params;
  const { courseId, period = 'monthly', startDate, endDate } = req.query;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only access your own student engagement data');
  }

  const dateRange = startDate && endDate ? {
    startDate: new Date(startDate as string),
    endDate: new Date(endDate as string),
  } : undefined;

  const engagement = await analyticsService.getStudentEngagementSummary(
    teacherId,
    courseId as string,
    dateRange
  );

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Student engagement data retrieved successfully',
    data: engagement,
  });
});

/**
 * Get activity feed for teacher
 */
const getActivityFeed = catchAsync(async (req: Request, res: Response) => {
  const { teacherId } = req.params;
  const { 
    limit = 20,
    offset = 0,
    type,
    priority,
    isRead,
    courseId,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only access your own activity feed');
  }

  const activities = await activityTrackingService.getRecentActivities(
    teacherId,
    parseInt(limit as string),
    parseInt(offset as string)
  );

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Activity feed retrieved successfully',
    data: {
      activities,
      pagination: {
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        total: activities.length,
      },
    },
  });
});

/**
 * Mark activity as read
 */
const markActivityAsRead = catchAsync(async (req: Request, res: Response) => {
  const { teacherId, activityId } = req.params;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only mark your own activities as read');
  }

  await activityTrackingService.markActivityAsRead(activityId, teacherId);

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Activity marked as read successfully',
    data: null,
  });
});

/**
 * Get analytics dashboard summary
 */
const getDashboardSummary = catchAsync(async (req: Request, res: Response) => {
  const { teacherId } = req.params;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only access your own dashboard');
  }

  // Get summary data for the current month
  const filters = {
    teacherId,
    period: 'monthly' as const,
  };

  const [analytics, recentActivities] = await Promise.all([
    analyticsService.getTeacherAnalytics(filters),
    activityTrackingService.getRecentActivities(teacherId, 10),
  ]);

  const summary = {
    overview: {
      totalRevenue: analytics.revenueAnalytics.totalRevenue,
      totalStudents: analytics.performanceMetrics.totalStudents,
      averageRating: analytics.performanceMetrics.averageRating,
      totalCourses: analytics.courseAnalytics.length,
    },
    recentActivities: recentActivities.slice(0, 5),
    topPerformingCourses: analytics.courseAnalytics
      .sort((a, b) => b.enrollments - a.enrollments)
      .slice(0, 3),
    insights: analytics.insights,
  };

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Dashboard summary retrieved successfully',
    data: summary,
  });
});

/**
 * Export analytics data
 */
const exportAnalytics = catchAsync(async (req: Request, res: Response) => {
  const { teacherId } = req.params;
  const { format = 'json', period = 'monthly', courseId } = req.query;

  // Validate teacher ID matches authenticated user
  const user = (req as any).user;
  if (user.role === 'teacher' && user.teacherId !== teacherId) {
    throw new AppError(httpStatus.FORBIDDEN, 'You can only export your own analytics');
  }

  const filters = {
    teacherId,
    courseId: courseId as string,
    period: period as 'daily' | 'weekly' | 'monthly' | 'yearly',
  };

  const analytics = await analyticsService.getTeacherAnalytics(filters);

  if (format === 'csv') {
    // TODO: Implement CSV export
    throw new AppError(httpStatus.NOT_IMPLEMENTED, 'CSV export not yet implemented');
  }

  sendResponse(res, {
    statusCode: httpStatus.OK,
    success: true,
    message: 'Analytics data exported successfully',
    data: analytics,
  });
});

export const AnalyticsController = {
  getTeacherAnalytics,
  getCourseAnalytics,
  getRevenueAnalytics,
  getPerformanceMetrics,
  getStudentEngagement,
  getActivityFeed,
  markActivityAsRead,
  getDashboardSummary,
  exportAnalytics,
};
