import { Types } from 'mongoose';
import { Activity } from '../../modules/Analytics/analytics.model';
import { IActivity, ActivityType, ActivityPriority } from '../../modules/Analytics/analytics.interface';
import { Logger } from '../../utils/logger';
import WebSocketService from '../websocket/WebSocketService';
import { redisOperations } from '../../config/redis';

interface ActivityData {
  teacherId: string;
  courseId?: string;
  studentId?: string;
  type: ActivityType;
  title: string;
  description: string;
  metadata?: Record<string, any>;
  priority?: ActivityPriority;
  actionRequired?: boolean;
  actionUrl?: string;
  relatedEntity: {
    entityType: 'course' | 'student' | 'payment' | 'review';
    entityId: string;
  };
}

class ActivityTrackingService {
  private webSocketService: WebSocketService | null = null;

  constructor(webSocketService?: WebSocketService) {
    this.webSocketService = webSocketService || null;
  }

  public setWebSocketService(webSocketService: WebSocketService): void {
    this.webSocketService = webSocketService;
  }

  /**
   * Track a new activity and broadcast it in real-time
   */
  public async trackActivity(activityData: ActivityData): Promise<IActivity> {
    try {
      // Create activity record
      const activity = new Activity({
        teacherId: new Types.ObjectId(activityData.teacherId),
        courseId: activityData.courseId ? new Types.ObjectId(activityData.courseId) : undefined,
        studentId: activityData.studentId ? new Types.ObjectId(activityData.studentId) : undefined,
        type: activityData.type,
        priority: activityData.priority || ActivityPriority.MEDIUM,
        title: activityData.title,
        description: activityData.description,
        metadata: activityData.metadata || {},
        isRead: false,
        actionRequired: activityData.actionRequired || false,
        actionUrl: activityData.actionUrl,
        relatedEntity: {
          entityType: activityData.relatedEntity.entityType,
          entityId: new Types.ObjectId(activityData.relatedEntity.entityId),
        },
      });

      const savedActivity = await activity.save();

      // Cache recent activities for quick access
      await this.cacheRecentActivity(activityData.teacherId, savedActivity);

      // Broadcast real-time update
      if (this.webSocketService) {
        this.webSocketService.broadcastActivityUpdate(activityData.teacherId, {
          id: savedActivity._id,
          type: savedActivity.type,
          priority: savedActivity.priority,
          title: savedActivity.title,
          description: savedActivity.description,
          metadata: savedActivity.metadata,
          actionRequired: savedActivity.actionRequired,
          actionUrl: savedActivity.actionUrl,
          createdAt: savedActivity.createdAt,
        });
      }

      Logger.info(`📊 Activity tracked: ${activityData.type} for teacher ${activityData.teacherId}`);
      return savedActivity;
    } catch (error) {
      Logger.error('❌ Failed to track activity:', error);
      throw error;
    }
  }

  /**
   * Track enrollment activity
   */
  public async trackEnrollment(teacherId: string, courseId: string, studentId: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackActivity({
      teacherId,
      courseId,
      studentId,
      type: ActivityType.ENROLLMENT,
      title: 'New Student Enrollment',
      description: 'A new student has enrolled in your course',
      metadata: {
        enrollmentDate: new Date(),
        ...metadata,
      },
      priority: ActivityPriority.MEDIUM,
      actionRequired: false,
      relatedEntity: {
        entityType: 'student',
        entityId: studentId,
      },
    });
  }

  /**
   * Track course completion activity
   */
  public async trackCompletion(teacherId: string, courseId: string, studentId: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackActivity({
      teacherId,
      courseId,
      studentId,
      type: ActivityType.COMPLETION,
      title: 'Course Completed',
      description: 'A student has completed your course',
      metadata: {
        completionDate: new Date(),
        completionRate: metadata?.completionRate || 100,
        ...metadata,
      },
      priority: ActivityPriority.HIGH,
      actionRequired: false,
      relatedEntity: {
        entityType: 'student',
        entityId: studentId,
      },
    });
  }

  /**
   * Track payment activity
   */
  public async trackPayment(teacherId: string, courseId: string, studentId: string, amount: number, metadata?: Record<string, any>): Promise<void> {
    await this.trackActivity({
      teacherId,
      courseId,
      studentId,
      type: ActivityType.PAYMENT,
      title: 'Payment Received',
      description: `Payment of $${amount} received for course enrollment`,
      metadata: {
        amount,
        paymentDate: new Date(),
        ...metadata,
      },
      priority: ActivityPriority.HIGH,
      actionRequired: false,
      relatedEntity: {
        entityType: 'payment',
        entityId: metadata?.paymentId || courseId,
      },
    });
  }

  /**
   * Track review activity
   */
  public async trackReview(teacherId: string, courseId: string, studentId: string, rating: number, metadata?: Record<string, any>): Promise<void> {
    const priority = rating >= 4 ? ActivityPriority.MEDIUM : ActivityPriority.HIGH;
    const actionRequired = rating < 3;

    await this.trackActivity({
      teacherId,
      courseId,
      studentId,
      type: ActivityType.REVIEW,
      title: `New ${rating}-Star Review`,
      description: `A student left a ${rating}-star review for your course`,
      metadata: {
        rating,
        reviewDate: new Date(),
        ...metadata,
      },
      priority,
      actionRequired,
      relatedEntity: {
        entityType: 'review',
        entityId: metadata?.reviewId || courseId,
      },
    });
  }

  /**
   * Track question activity
   */
  public async trackQuestion(teacherId: string, courseId: string, studentId: string, questionTitle: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackActivity({
      teacherId,
      courseId,
      studentId,
      type: ActivityType.QUESTION,
      title: 'New Student Question',
      description: `Student asked: "${questionTitle}"`,
      metadata: {
        questionTitle,
        questionDate: new Date(),
        ...metadata,
      },
      priority: ActivityPriority.HIGH,
      actionRequired: true,
      actionUrl: `/teacher/courses/${courseId}/questions/${metadata?.questionId}`,
      relatedEntity: {
        entityType: 'course',
        entityId: courseId,
      },
    });
  }

  /**
   * Track course update activity
   */
  public async trackCourseUpdate(teacherId: string, courseId: string, updateType: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackActivity({
      teacherId,
      courseId,
      type: ActivityType.COURSE_UPDATE,
      title: 'Course Updated',
      description: `Course ${updateType} has been updated`,
      metadata: {
        updateType,
        updateDate: new Date(),
        ...metadata,
      },
      priority: ActivityPriority.LOW,
      actionRequired: false,
      relatedEntity: {
        entityType: 'course',
        entityId: courseId,
      },
    });
  }

  /**
   * Track certificate generation activity
   */
  public async trackCertificate(teacherId: string, courseId: string, studentId: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackActivity({
      teacherId,
      courseId,
      studentId,
      type: ActivityType.CERTIFICATE,
      title: 'Certificate Generated',
      description: 'A completion certificate has been generated for a student',
      metadata: {
        certificateDate: new Date(),
        ...metadata,
      },
      priority: ActivityPriority.MEDIUM,
      actionRequired: false,
      relatedEntity: {
        entityType: 'student',
        entityId: studentId,
      },
    });
  }

  /**
   * Track refund activity
   */
  public async trackRefund(teacherId: string, courseId: string, studentId: string, amount: number, metadata?: Record<string, any>): Promise<void> {
    await this.trackActivity({
      teacherId,
      courseId,
      studentId,
      type: ActivityType.REFUND,
      title: 'Refund Processed',
      description: `Refund of $${amount} has been processed`,
      metadata: {
        amount,
        refundDate: new Date(),
        reason: metadata?.reason || 'Not specified',
        ...metadata,
      },
      priority: ActivityPriority.HIGH,
      actionRequired: true,
      relatedEntity: {
        entityType: 'payment',
        entityId: metadata?.paymentId || courseId,
      },
    });
  }

  /**
   * Track message activity
   */
  public async trackMessage(teacherId: string, courseId: string, studentId: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackActivity({
      teacherId,
      courseId,
      studentId,
      type: ActivityType.MESSAGE,
      title: 'New Message',
      description: 'You have received a new message from a student',
      metadata: {
        messageDate: new Date(),
        ...metadata,
      },
      priority: ActivityPriority.MEDIUM,
      actionRequired: true,
      actionUrl: `/teacher/messages/${metadata?.conversationId}`,
      relatedEntity: {
        entityType: 'student',
        entityId: studentId,
      },
    });
  }

  /**
   * Mark activity as read
   */
  public async markActivityAsRead(activityId: string, teacherId: string): Promise<void> {
    try {
      await Activity.findOneAndUpdate(
        { _id: activityId, teacherId: new Types.ObjectId(teacherId) },
        { isRead: true },
        { new: true }
      );

      // Update cache
      await this.updateActivityCache(teacherId, activityId, { isRead: true });

      Logger.info(`📖 Activity marked as read: ${activityId}`);
    } catch (error) {
      Logger.error('❌ Failed to mark activity as read:', error);
      throw error;
    }
  }

  /**
   * Get recent activities for a teacher
   */
  public async getRecentActivities(teacherId: string, limit: number = 20, offset: number = 0): Promise<IActivity[]> {
    try {
      // Try to get from cache first
      const cacheKey = `activities:teacher:${teacherId}:${limit}:${offset}`;
      const cached = await redisOperations.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      // Fetch from database
      const activities = await Activity.find({ teacherId: new Types.ObjectId(teacherId) })
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(offset)
        .populate('courseId', 'title')
        .populate('studentId', 'name email')
        .lean();

      // Cache for 5 minutes
      await redisOperations.setex(cacheKey, 300, JSON.stringify(activities));

      return activities as IActivity[];
    } catch (error) {
      Logger.error('❌ Failed to get recent activities:', error);
      throw error;
    }
  }

  /**
   * Cache recent activity for quick access
   */
  private async cacheRecentActivity(teacherId: string, activity: IActivity): Promise<void> {
    try {
      const cacheKey = `activities:teacher:${teacherId}:recent`;
      const activities = await redisOperations.get(cacheKey);
      
      let activityList: any[] = activities ? JSON.parse(activities) : [];
      
      // Add new activity to the beginning
      activityList.unshift(activity);
      
      // Keep only the most recent 50 activities
      activityList = activityList.slice(0, 50);
      
      // Cache for 1 hour
      await redisOperations.setex(cacheKey, 3600, JSON.stringify(activityList));
    } catch (error) {
      Logger.error('❌ Failed to cache recent activity:', error);
    }
  }

  /**
   * Update activity in cache
   */
  private async updateActivityCache(teacherId: string, activityId: string, updates: Partial<IActivity>): Promise<void> {
    try {
      const cacheKey = `activities:teacher:${teacherId}:recent`;
      const activities = await redisOperations.get(cacheKey);
      
      if (activities) {
        const activityList = JSON.parse(activities);
        const index = activityList.findIndex((a: any) => a._id.toString() === activityId);
        
        if (index !== -1) {
          activityList[index] = { ...activityList[index], ...updates };
          await redisOperations.setex(cacheKey, 3600, JSON.stringify(activityList));
        }
      }
    } catch (error) {
      Logger.error('❌ Failed to update activity cache:', error);
    }
  }
}

export default ActivityTrackingService;
