import { Types } from 'mongoose';
import {
  Message,
  Conversation,
  MessageThread,
  MessageNotification,
  MessageSearchIndex
} from '../../modules/Messaging/messaging.model';
import {
  IMessage,
  IConversation,
  ICreateConversationDTO,
  ISendMessageDTO,
  IConversationResponseDTO,
  IMessageResponseDTO,
  IConversationQuery,
  IMessageQuery,
  MessageStatus,
  MessageType
} from '../../modules/Messaging/messaging.interface';
import { Student } from '../../modules/Student/student.model';
import { Teacher } from '../../modules/Teacher/teacher.model';
import { Course } from '../../modules/Course/course.model';
import { Logger } from '../../utils/logger';
import MessagingValidationService from './MessagingValidationService';
import FileUploadService from './FileUploadService';
import WebSocketService from '../websocket/WebSocketService';
import ActivityTrackingService from '../activity/ActivityTrackingService';
import { redisOperations } from '../../config/redis';
import AppError from '../../errors/AppError';
import httpStatus from 'http-status';

class MessagingService {
  private validationService: MessagingValidationService;
  private fileUploadService: FileUploadService;
  private webSocketService: WebSocketService | null = null;
  private activityTrackingService: ActivityTrackingService | null = null;

  constructor() {
    this.validationService = new MessagingValidationService();
    this.fileUploadService = new FileUploadService();
  }

  public setWebSocketService(webSocketService: WebSocketService): void {
    this.webSocketService = webSocketService;
  }

  public setActivityTrackingService(activityTrackingService: ActivityTrackingService): void {
    this.activityTrackingService = activityTrackingService;
  }

  /**
   * Create a new conversation between student and teacher
   */
  public async createConversation(data: ICreateConversationDTO): Promise<IConversationResponseDTO> {
    try {
      // Validate that student is enrolled in the course
      await this.validationService.validateConversationCreation(
        data.teacherId,
        data.studentId,
        data.courseId
      );

      // Check if conversation already exists
      const existingConversation = await Conversation.findOne({
        teacherId: new Types.ObjectId(data.teacherId),
        studentId: new Types.ObjectId(data.studentId),
        courseId: new Types.ObjectId(data.courseId),
      });

      if (existingConversation) {
        return this.formatConversationResponse(existingConversation);
      }

      // Get course and participant details
      const [course, teacher, student] = await Promise.all([
        Course.findById(data.courseId).select('title'),
        Teacher.findById(data.teacherId).populate('user', 'email'),
        Student.findById(data.studentId).populate('user', 'email'),
      ]);

      if (!course || !teacher || !student) {
        throw new AppError(httpStatus.NOT_FOUND, 'Course, teacher, or student not found');
      }

      // Create conversation
      const conversation = new Conversation({
        courseId: new Types.ObjectId(data.courseId),
        teacherId: new Types.ObjectId(data.teacherId),
        studentId: new Types.ObjectId(data.studentId),
        title: data.title || `${course.title} - Discussion`,
        participants: [
          {
            userId: new Types.ObjectId(data.teacherId),
            userType: 'teacher',
            joinedAt: new Date(),
            role: 'admin',
          },
          {
            userId: new Types.ObjectId(data.studentId),
            userType: 'student',
            joinedAt: new Date(),
            role: 'member',
          },
        ],
        metadata: {
          totalMessages: 0,
          totalFiles: 0,
          createdBy: new Types.ObjectId(data.studentId),
        },
      });

      const savedConversation = await conversation.save();

      // Send initial message if provided
      if (data.initialMessage) {
        await this.sendMessage({
          conversationId: savedConversation._id.toString(),
          content: data.initialMessage,
          messageType: MessageType.TEXT,
        }, data.studentId, 'student');
      }

      // Track activity
      if (this.activityTrackingService) {
        await this.activityTrackingService.trackMessage(
          data.teacherId,
          data.courseId,
          data.studentId,
          { conversationId: savedConversation._id.toString() }
        );
      }

      Logger.info(`💬 Conversation created: ${savedConversation._id}`);
      return this.formatConversationResponse(savedConversation);
    } catch (error) {
      Logger.error('❌ Failed to create conversation:', error);
      throw error;
    }
  }

  /**
   * Send a message in a conversation
   */
  public async sendMessage(
    data: ISendMessageDTO,
    senderId: string,
    senderType: 'student' | 'teacher',
    files?: Express.Multer.File[]
  ): Promise<IMessageResponseDTO> {
    try {
      // Get conversation and validate permissions
      const conversation = await Conversation.findById(data.conversationId);
      if (!conversation) {
        throw new AppError(httpStatus.NOT_FOUND, 'Conversation not found');
      }

      // Validate sender is part of conversation
      const isParticipant = conversation.participants.some(
        p => p.userId.toString() === senderId && p.userType === senderType
      );

      if (!isParticipant) {
        throw new AppError(httpStatus.FORBIDDEN, 'You are not a participant in this conversation');
      }

      // Validate enrollment if sender is student
      if (senderType === 'student') {
        const validation = await this.validationService.validateStudentEnrollment(
          senderId,
          conversation.teacherId.toString(),
          conversation.courseId.toString()
        );

        if (!validation.isValid) {
          throw new AppError(httpStatus.FORBIDDEN, 'Student is not enrolled in this course');
        }
      }

      // Check rate limiting
      const canSend = await this.validationService.checkRateLimit(senderId, senderType);
      if (!canSend) {
        throw new AppError(httpStatus.TOO_MANY_REQUESTS, 'Rate limit exceeded');
      }

      // Process file attachments
      const attachments = files ? await this.fileUploadService.processUploadedFiles(
        files,
        senderType,
        data.conversationId
      ) : [];

      // Sanitize message content
      const sanitizedContent = this.validationService.sanitizeMessageContent(data.content);

      // Determine receiver
      const receiverId = senderType === 'student' 
        ? conversation.teacherId 
        : conversation.studentId;
      const receiverType = senderType === 'student' ? 'teacher' : 'student';

      // Create message
      const message = new Message({
        conversationId: new Types.ObjectId(data.conversationId),
        senderId: new Types.ObjectId(senderId),
        senderType,
        receiverId,
        receiverType,
        courseId: conversation.courseId,
        messageType: data.messageType || MessageType.TEXT,
        content: sanitizedContent,
        attachments,
        status: MessageStatus.SENT,
        replyTo: data.replyTo ? new Types.ObjectId(data.replyTo) : undefined,
        metadata: {
          deviceInfo: 'web', // TODO: Get from request headers
          ipAddress: '127.0.0.1', // TODO: Get from request
        },
      });

      const savedMessage = await message.save();

      // Update conversation
      await Conversation.findByIdAndUpdate(data.conversationId, {
        lastMessage: savedMessage._id,
        lastMessageAt: new Date(),
        $inc: {
          [`unreadCount.${receiverType}`]: 1,
          'metadata.totalMessages': 1,
          'metadata.totalFiles': attachments.length,
        },
      });

      // Create search index
      await this.createMessageSearchIndex(savedMessage);

      // Create notification
      await this.createMessageNotification(savedMessage, conversation);

      // Broadcast real-time update
      if (this.webSocketService) {
        this.webSocketService.broadcastNewMessage(data.conversationId, {
          id: savedMessage._id,
          content: savedMessage.content,
          senderId: savedMessage.senderId,
          senderType: savedMessage.senderType,
          messageType: savedMessage.messageType,
          attachments: savedMessage.attachments,
          createdAt: savedMessage.createdAt,
        });
      }

      Logger.info(`📨 Message sent: ${savedMessage._id} in conversation ${data.conversationId}`);
      return this.formatMessageResponse(savedMessage);
    } catch (error) {
      Logger.error('❌ Failed to send message:', error);
      throw error;
    }
  }

  /**
   * Get conversations for a user
   */
  public async getConversations(
    userId: string,
    userType: 'student' | 'teacher',
    query: IConversationQuery
  ): Promise<{ conversations: IConversationResponseDTO[]; total: number }> {
    try {
      const filter: any = {
        [`${userType}Id`]: new Types.ObjectId(userId),
        isActive: query.isActive !== undefined ? query.isActive : true,
      };

      if (query.courseId) {
        filter.courseId = new Types.ObjectId(query.courseId);
      }

      if (query.isArchived !== undefined) {
        filter.isArchived = query.isArchived;
      }

      const sortField = query.sortBy || 'lastMessageAt';
      const sortOrder = query.sortOrder === 'asc' ? 1 : -1;

      const [conversations, total] = await Promise.all([
        Conversation.find(filter)
          .populate('courseId', 'title')
          .populate('teacherId', 'name email')
          .populate('studentId', 'name email')
          .populate('lastMessage', 'content createdAt')
          .sort({ [sortField]: sortOrder })
          .limit(query.limit || 20)
          .skip(query.offset || 0),
        Conversation.countDocuments(filter),
      ]);

      const formattedConversations = conversations.map(conv => this.formatConversationResponse(conv));

      return { conversations: formattedConversations, total };
    } catch (error) {
      Logger.error('❌ Failed to get conversations:', error);
      throw error;
    }
  }

  /**
   * Get messages in a conversation
   */
  public async getMessages(
    conversationId: string,
    userId: string,
    userType: 'student' | 'teacher',
    query: IMessageQuery
  ): Promise<{ messages: IMessageResponseDTO[]; total: number }> {
    try {
      // Validate user is part of conversation
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        throw new AppError(httpStatus.NOT_FOUND, 'Conversation not found');
      }

      const isParticipant = conversation.participants.some(
        p => p.userId.toString() === userId && p.userType === userType
      );

      if (!isParticipant) {
        throw new AppError(httpStatus.FORBIDDEN, 'You are not a participant in this conversation');
      }

      const filter: any = { conversationId: new Types.ObjectId(conversationId) };

      if (query.messageType) {
        filter.messageType = query.messageType;
      }

      if (query.hasAttachments) {
        filter['attachments.0'] = { $exists: true };
      }

      if (query.dateFrom || query.dateTo) {
        filter.createdAt = {};
        if (query.dateFrom) filter.createdAt.$gte = query.dateFrom;
        if (query.dateTo) filter.createdAt.$lte = query.dateTo;
      }

      const sortField = query.sortBy || 'createdAt';
      const sortOrder = query.sortOrder === 'asc' ? 1 : -1;

      const [messages, total] = await Promise.all([
        Message.find(filter)
          .populate('replyTo', 'content senderId senderType')
          .sort({ [sortField]: sortOrder })
          .limit(query.limit || 50)
          .skip(query.offset || 0),
        Message.countDocuments(filter),
      ]);

      // Mark messages as read
      await this.markMessagesAsRead(conversationId, userId, userType);

      const formattedMessages = await Promise.all(
        messages.map(msg => this.formatMessageResponse(msg))
      );

      return { messages: formattedMessages, total };
    } catch (error) {
      Logger.error('❌ Failed to get messages:', error);
      throw error;
    }
  }

  /**
   * Mark messages as read
   */
  public async markMessagesAsRead(
    conversationId: string,
    userId: string,
    userType: 'student' | 'teacher'
  ): Promise<void> {
    try {
      // Update unread messages to read
      await Message.updateMany(
        {
          conversationId: new Types.ObjectId(conversationId),
          receiverId: new Types.ObjectId(userId),
          receiverType: userType,
          status: { $ne: MessageStatus.READ },
        },
        {
          status: MessageStatus.READ,
          readAt: new Date(),
        }
      );

      // Reset unread count for user
      await Conversation.findByIdAndUpdate(conversationId, {
        [`unreadCount.${userType}`]: 0,
      });

      // Broadcast read status
      if (this.webSocketService) {
        this.webSocketService.broadcastToConversation(conversationId, 'messages_read', {
          userId,
          userType,
          readAt: new Date(),
        });
      }

      Logger.info(`📖 Messages marked as read in conversation ${conversationId} by ${userType} ${userId}`);
    } catch (error) {
      Logger.error('❌ Failed to mark messages as read:', error);
      throw error;
    }
  }

  /**
   * Search messages
   */
  public async searchMessages(
    userId: string,
    userType: 'student' | 'teacher',
    searchTerm: string,
    courseId?: string
  ): Promise<IMessageResponseDTO[]> {
    try {
      const filter: any = {
        [`${userType}Id`]: new Types.ObjectId(userId),
        $text: { $search: searchTerm },
      };

      if (courseId) {
        filter.courseId = new Types.ObjectId(courseId);
      }

      const searchResults = await MessageSearchIndex.find(filter)
        .limit(50)
        .sort({ createdAt: -1 });

      const messageIds = searchResults.map(result => result.messageId);
      
      const messages = await Message.find({ _id: { $in: messageIds } })
        .populate('replyTo', 'content senderId senderType')
        .sort({ createdAt: -1 });

      return Promise.all(messages.map(msg => this.formatMessageResponse(msg)));
    } catch (error) {
      Logger.error('❌ Failed to search messages:', error);
      return [];
    }
  }

  // Private helper methods
  private async createMessageSearchIndex(message: IMessage): Promise<void> {
    try {
      const searchIndex = new MessageSearchIndex({
        messageId: message._id,
        conversationId: message.conversationId,
        courseId: message.courseId,
        teacherId: message.senderType === 'teacher' ? message.senderId : message.receiverId,
        studentId: message.senderType === 'student' ? message.senderId : message.receiverId,
        content: message.content,
        searchableContent: message.content.toLowerCase(),
        attachmentNames: message.attachments.map(att => att.originalName),
        tags: [], // TODO: Extract tags from content
        createdAt: message.createdAt || new Date(),
      });

      await searchIndex.save();
    } catch (error) {
      Logger.error('❌ Failed to create message search index:', error);
    }
  }

  private async createMessageNotification(message: IMessage, conversation: IConversation): Promise<void> {
    try {
      // Get sender and receiver details
      const [sender, receiver] = await Promise.all([
        message.senderType === 'teacher' 
          ? Teacher.findById(message.senderId).select('name')
          : Student.findById(message.senderId).select('name'),
        message.receiverType === 'teacher'
          ? Teacher.findById(message.receiverId).select('name')
          : Student.findById(message.receiverId).select('name'),
      ]);

      const course = await Course.findById(conversation.courseId).select('title');

      if (!sender || !receiver || !course) return;

      const senderName = `${(sender as any).name.firstName} ${(sender as any).name.lastName}`;
      const courseName = course.title;

      const notification = new MessageNotification({
        userId: message.receiverId,
        userType: message.receiverType,
        messageId: message._id,
        conversationId: message.conversationId,
        type: 'new_message',
        title: `New message from ${senderName}`,
        content: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : ''),
        actionUrl: `/messages/${conversation._id}`,
        metadata: {
          senderName,
          courseName,
        },
      });

      await notification.save();
    } catch (error) {
      Logger.error('❌ Failed to create message notification:', error);
    }
  }

  private formatConversationResponse(conversation: any): IConversationResponseDTO {
    return {
      id: conversation._id.toString(),
      courseId: conversation.courseId._id?.toString() || conversation.courseId.toString(),
      courseName: conversation.courseId.title || 'Unknown Course',
      teacherId: conversation.teacherId._id?.toString() || conversation.teacherId.toString(),
      teacherName: conversation.teacherId.name 
        ? `${conversation.teacherId.name.firstName} ${conversation.teacherId.name.lastName}`
        : 'Unknown Teacher',
      studentId: conversation.studentId._id?.toString() || conversation.studentId.toString(),
      studentName: conversation.studentId.name
        ? `${conversation.studentId.name.firstName} ${conversation.studentId.name.lastName}`
        : 'Unknown Student',
      title: conversation.title,
      lastMessage: conversation.lastMessage ? {
        id: conversation.lastMessage._id?.toString() || conversation.lastMessage.toString(),
        content: conversation.lastMessage.content || '',
        senderName: 'Unknown',
        createdAt: conversation.lastMessage.createdAt || new Date(),
      } : undefined,
      unreadCount: conversation.unreadCount?.teacher || conversation.unreadCount?.student || 0,
      isActive: conversation.isActive,
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
    };
  }

  private async formatMessageResponse(message: any): Promise<IMessageResponseDTO> {
    // Get sender details
    const sender = message.senderType === 'teacher'
      ? await Teacher.findById(message.senderId).select('name')
      : await Student.findById(message.senderId).select('name');

    const senderName = sender 
      ? `${(sender as any).name.firstName} ${(sender as any).name.lastName}`
      : 'Unknown User';

    return {
      id: message._id.toString(),
      conversationId: message.conversationId.toString(),
      senderId: message.senderId.toString(),
      senderName,
      senderType: message.senderType,
      content: message.content,
      messageType: message.messageType,
      attachments: message.attachments || [],
      status: message.status,
      isEdited: message.isEdited || false,
      replyTo: message.replyTo ? {
        id: message.replyTo._id?.toString() || message.replyTo.toString(),
        content: message.replyTo.content || '',
        senderName: 'Unknown',
      } : undefined,
      readAt: message.readAt,
      createdAt: message.createdAt,
      updatedAt: message.updatedAt,
    };
  }
}

export default MessagingService;
