import { Types } from 'mongoose';
import {
  CourseAnalytics,
  StudentEngagement,
  RevenueAnalytics,
  PerformanceMetrics,
  AnalyticsSummary
} from '../../modules/Analytics/analytics.model';
import {
  ICourseAnalytics,
  IStudentEngagement,
  IRevenueAnalytics,
  IPerformanceMetrics,
  IAnalyticsSummary,
  IAnalyticsQuery
} from '../../modules/Analytics/analytics.interface';
import { Course } from '../../modules/Course/course.model';
import { Student } from '../../modules/Student/student.model';
import { Payment } from '../../modules/Payment/payment.model';
import { Transaction } from '../../modules/Payment/transaction.model';
import { Logger } from '../../utils/logger';
import { redisOperations } from '../../config/redis';
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, subDays } from 'date-fns';

interface DateRange {
  startDate: Date;
  endDate: Date;
}

interface AnalyticsFilters {
  teacherId: string;
  courseId?: string;
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  startDate?: Date;
  endDate?: Date;
  compareWithPrevious?: boolean;
}

class AnalyticsService {
  private readonly CACHE_TTL = {
    realtime: 300, // 5 minutes
    hourly: 3600, // 1 hour
    daily: 86400, // 24 hours
    weekly: 604800, // 7 days
  };

  /**
   * Invalidate cache for teacher analytics
   */
  public async invalidateTeacherCache(teacherId: string): Promise<void> {
    try {
      const patterns = [
        `teacher_analytics:${teacherId}:*`,
        `activities:teacher:${teacherId}:*`,
        `course_analytics:${teacherId}:*`,
        `revenue_analytics:${teacherId}:*`,
        `performance_metrics:${teacherId}:*`,
      ];

      for (const pattern of patterns) {
        // Note: This is a simplified cache invalidation
        // In production, you might want to use Redis SCAN with pattern matching
        await redisOperations.del(pattern);
      }

      Logger.info(`🗑️ Cache invalidated for teacher: ${teacherId}`);
    } catch (error) {
      Logger.error('❌ Failed to invalidate teacher cache:', error);
    }
  }

  /**
   * Warm up cache for frequently accessed data
   */
  public async warmUpCache(teacherId: string): Promise<void> {
    try {
      Logger.info(`🔥 Warming up cache for teacher: ${teacherId}`);

      // Pre-load monthly analytics
      const monthlyFilters = {
        teacherId,
        period: 'monthly' as const,
      };

      await this.getTeacherAnalytics(monthlyFilters);

      // Pre-load recent activities
      const activityTrackingService = new (await import('../activity/ActivityTrackingService')).default();
      await activityTrackingService.getRecentActivities(teacherId, 20);

      Logger.info(`✅ Cache warmed up for teacher: ${teacherId}`);
    } catch (error) {
      Logger.error('❌ Failed to warm up cache:', error);
    }
  }

  /**
   * Get comprehensive analytics for a teacher
   */
  public async getTeacherAnalytics(filters: AnalyticsFilters): Promise<IAnalyticsSummary> {
    try {
      const cacheKey = this.generateCacheKey('teacher_analytics', filters);
      const cached = await redisOperations.get(cacheKey);
      
      if (cached) {
        Logger.info(`📊 Returning cached analytics for teacher: ${filters.teacherId}`);
        return JSON.parse(cached);
      }

      const dateRange = this.getDateRange(filters.period, filters.startDate, filters.endDate);
      
      // Fetch all analytics data in parallel
      const [courseAnalytics, revenueAnalytics, performanceMetrics, studentEngagement] = await Promise.all([
        this.getCourseAnalytics(filters.teacherId, filters.courseId, dateRange),
        this.getRevenueAnalytics(filters.teacherId, filters.courseId, dateRange),
        this.getPerformanceMetrics(filters.teacherId, filters.courseId, dateRange),
        this.getStudentEngagementSummary(filters.teacherId, filters.courseId, dateRange),
      ]);

      // Generate insights and recommendations
      const insights = await this.generateInsights(courseAnalytics, revenueAnalytics, performanceMetrics);

      const summary: IAnalyticsSummary = {
        teacherId: new Types.ObjectId(filters.teacherId),
        period: filters.period,
        dateRange,
        courseAnalytics: courseAnalytics.map(ca => ({
          courseId: ca.courseId,
          courseName: (ca as any).courseName || 'Unknown Course',
          enrollments: ca.totalEnrollments,
          revenue: 0, // Will be populated from revenue analytics
          completionRate: ca.completionRate,
          rating: 0, // Will be populated from performance metrics
        })),
        revenueAnalytics: {
          totalRevenue: revenueAnalytics?.totalRevenue || 0,
          growth: 0, // Will be calculated if comparing with previous period
          topCourse: revenueAnalytics?.topPerformingCourses?.[0]?.courseId,
          averageOrderValue: revenueAnalytics?.averageOrderValue || 0,
        },
        performanceMetrics: {
          averageRating: performanceMetrics?.averageRating || 0,
          totalStudents: await this.getTotalStudentsCount(filters.teacherId, filters.courseId),
          completionRate: performanceMetrics?.courseCompletionRate || 0,
          satisfactionScore: performanceMetrics?.studentSatisfactionScore || 0,
        },
        studentEngagement: {
          totalActiveStudents: studentEngagement.totalActiveStudents,
          averageEngagementScore: studentEngagement.averageEngagementScore,
          topPerformingCourses: studentEngagement.topPerformingCourses,
          retentionRate: studentEngagement.retentionRate,
        },
        insights,
        generatedAt: new Date(),
      } as IAnalyticsSummary;

      // Cache the result
      await redisOperations.setex(cacheKey, this.CACHE_TTL.hourly, JSON.stringify(summary));

      Logger.info(`✅ Generated analytics for teacher: ${filters.teacherId}`);
      return summary;
    } catch (error) {
      Logger.error('❌ Failed to get teacher analytics:', error);
      throw error;
    }
  }

  /**
   * Get course analytics with enrollment and engagement data
   */
  public async getCourseAnalytics(
    teacherId: string,
    courseId?: string,
    dateRange?: DateRange
  ): Promise<ICourseAnalytics[]> {
    try {
      const query: any = { teacherId: new Types.ObjectId(teacherId) };
      if (courseId) {
        query.courseId = new Types.ObjectId(courseId);
      }

      let analytics = await CourseAnalytics.find(query)
        .populate('courseId', 'title totalEnrollment')
        .sort({ lastUpdated: -1 });

      // If no analytics exist, generate them
      if (analytics.length === 0) {
        analytics = await this.generateCourseAnalytics(teacherId, courseId);
      }

      return analytics;
    } catch (error) {
      Logger.error('❌ Failed to get course analytics:', error);
      throw error;
    }
  }

  /**
   * Get revenue analytics with payment trends
   */
  public async getRevenueAnalytics(
    teacherId: string,
    courseId?: string,
    dateRange?: DateRange
  ): Promise<IRevenueAnalytics | null> {
    try {
      const query: any = { teacherId: new Types.ObjectId(teacherId) };
      if (courseId) {
        query.courseId = new Types.ObjectId(courseId);
      }

      let analytics = await RevenueAnalytics.findOne(query).sort({ lastUpdated: -1 });

      // If no analytics exist, generate them
      if (!analytics) {
        analytics = await this.generateRevenueAnalytics(teacherId, courseId, dateRange);
      }

      return analytics;
    } catch (error) {
      Logger.error('❌ Failed to get revenue analytics:', error);
      return null;
    }
  }

  /**
   * Get performance metrics including ratings and completion rates
   */
  public async getPerformanceMetrics(
    teacherId: string,
    courseId?: string,
    dateRange?: DateRange
  ): Promise<IPerformanceMetrics | null> {
    try {
      const query: any = { teacherId: new Types.ObjectId(teacherId) };
      if (courseId) {
        query.courseId = new Types.ObjectId(courseId);
      }

      let metrics = await PerformanceMetrics.findOne(query).sort({ lastUpdated: -1 });

      // If no metrics exist, generate them
      if (!metrics) {
        metrics = await this.generatePerformanceMetrics(teacherId, courseId);
      }

      return metrics;
    } catch (error) {
      Logger.error('❌ Failed to get performance metrics:', error);
      return null;
    }
  }

  /**
   * Get student engagement summary
   */
  public async getStudentEngagementSummary(
    teacherId: string,
    courseId?: string,
    dateRange?: DateRange
  ): Promise<{
    totalActiveStudents: number;
    averageEngagementScore: number;
    topPerformingCourses: Types.ObjectId[];
    retentionRate: number;
  }> {
    try {
      const query: any = { teacherId: new Types.ObjectId(teacherId) };
      if (courseId) {
        query.courseId = new Types.ObjectId(courseId);
      }

      const engagementData = await StudentEngagement.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            totalStudents: { $sum: 1 },
            averageEngagement: { $avg: '$engagementScore' },
            activeStudents: {
              $sum: {
                $cond: [
                  { $gte: ['$lastActivity', subDays(new Date(), 7)] },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);

      const summary = engagementData[0] || {
        totalStudents: 0,
        averageEngagement: 0,
        activeStudents: 0,
      };

      // Get top performing courses
      const topCourses = await StudentEngagement.aggregate([
        { $match: { teacherId: new Types.ObjectId(teacherId) } },
        {
          $group: {
            _id: '$courseId',
            averageEngagement: { $avg: '$engagementScore' },
            studentCount: { $sum: 1 }
          }
        },
        { $sort: { averageEngagement: -1 } },
        { $limit: 5 }
      ]);

      return {
        totalActiveStudents: summary.activeStudents,
        averageEngagementScore: summary.averageEngagement,
        topPerformingCourses: topCourses.map(course => course._id),
        retentionRate: summary.totalStudents > 0 ? (summary.activeStudents / summary.totalStudents) * 100 : 0,
      };
    } catch (error) {
      Logger.error('❌ Failed to get student engagement summary:', error);
      return {
        totalActiveStudents: 0,
        averageEngagementScore: 0,
        topPerformingCourses: [],
        retentionRate: 0,
      };
    }
  }

  /**
   * Generate course analytics from raw data
   */
  private async generateCourseAnalytics(teacherId: string, courseId?: string): Promise<ICourseAnalytics[]> {
    try {
      const query: any = { creator: new Types.ObjectId(teacherId) };
      if (courseId) {
        query._id = new Types.ObjectId(courseId);
      }

      const courses = await Course.find(query);
      const analytics: ICourseAnalytics[] = [];

      for (const course of courses) {
        // Calculate enrollment metrics
        const totalEnrollments = course.totalEnrollment || 0;
        const newEnrollments = await this.calculateNewEnrollments(course._id);
        
        // Calculate completion rate
        const completionRate = await this.calculateCompletionRate(course._id);
        
        // Calculate average time spent
        const averageTimeSpent = await this.calculateAverageTimeSpent(course._id);

        const courseAnalytics = new CourseAnalytics({
          courseId: course._id,
          teacherId: new Types.ObjectId(teacherId),
          totalEnrollments,
          newEnrollments,
          completionRate,
          averageTimeSpent,
          dropoffRate: 100 - completionRate,
          engagementMetrics: {
            averageSessionDuration: averageTimeSpent,
            totalSessions: totalEnrollments,
            bounceRate: 0, // TODO: Calculate actual bounce rate
            returnRate: 0, // TODO: Calculate actual return rate
          },
          lastUpdated: new Date(),
        });

        const saved = await courseAnalytics.save();
        analytics.push(saved);
      }

      return analytics;
    } catch (error) {
      Logger.error('❌ Failed to generate course analytics:', error);
      return [];
    }
  }

  /**
   * Generate revenue analytics from payment data
   */
  private async generateRevenueAnalytics(
    teacherId: string,
    courseId?: string,
    dateRange?: DateRange
  ): Promise<IRevenueAnalytics | null> {
    try {
      const query: any = { teacherId: new Types.ObjectId(teacherId) };
      if (courseId) {
        query.courseId = new Types.ObjectId(courseId);
      }

      // Get payment data
      const payments = await Payment.find(query);
      const totalRevenue = payments.reduce((sum, payment) => sum + payment.teacherShare, 0);
      
      // Calculate revenue by period
      const revenueByPeriod = await this.calculateRevenueByPeriod(teacherId, courseId);
      
      // Calculate average order value
      const averageOrderValue = payments.length > 0 ? totalRevenue / payments.length : 0;

      // Get top performing courses
      const topPerformingCourses = await this.getTopPerformingCoursesByRevenue(teacherId);

      const revenueAnalytics = new RevenueAnalytics({
        teacherId: new Types.ObjectId(teacherId),
        courseId: courseId ? new Types.ObjectId(courseId) : undefined,
        totalRevenue,
        revenueByPeriod,
        averageOrderValue,
        refundRate: 0, // TODO: Calculate actual refund rate
        conversionRate: 0, // TODO: Calculate actual conversion rate
        paymentTrends: [], // TODO: Calculate payment trends
        topPerformingCourses,
        lastUpdated: new Date(),
      });

      return await revenueAnalytics.save();
    } catch (error) {
      Logger.error('❌ Failed to generate revenue analytics:', error);
      return null;
    }
  }

  /**
   * Generate performance metrics from course and student data
   */
  private async generatePerformanceMetrics(teacherId: string, courseId?: string): Promise<IPerformanceMetrics | null> {
    try {
      // TODO: Implement actual performance metrics calculation
      // This would involve calculating ratings, reviews, completion rates, etc.
      
      const performanceMetrics = new PerformanceMetrics({
        teacherId: new Types.ObjectId(teacherId),
        courseId: courseId ? new Types.ObjectId(courseId) : undefined,
        averageRating: 4.5, // Placeholder
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        studentSatisfactionScore: 85,
        courseCompletionRate: 75,
        studentRetentionRate: 80,
        qualityMetrics: {
          contentQuality: 85,
          instructorRating: 90,
          courseStructure: 80,
          valueForMoney: 85,
        },
        competitiveMetrics: {
          marketPosition: 75,
          categoryRanking: 10,
          peerComparison: 80,
        },
        lastUpdated: new Date(),
      });

      return await performanceMetrics.save();
    } catch (error) {
      Logger.error('❌ Failed to generate performance metrics:', error);
      return null;
    }
  }

  /**
   * Helper methods for calculations
   */
  private async calculateNewEnrollments(courseId: Types.ObjectId): Promise<{
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  }> {
    // TODO: Implement actual enrollment calculation
    return { daily: 0, weekly: 0, monthly: 0, yearly: 0 };
  }

  private async calculateCompletionRate(courseId: Types.ObjectId): Promise<number> {
    // TODO: Implement actual completion rate calculation
    return 75; // Placeholder
  }

  private async calculateAverageTimeSpent(courseId: Types.ObjectId): Promise<number> {
    // TODO: Implement actual time spent calculation
    return 120; // Placeholder (minutes)
  }

  private async calculateRevenueByPeriod(teacherId: string, courseId?: string): Promise<{
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  }> {
    // TODO: Implement actual revenue calculation by period
    return { daily: 0, weekly: 0, monthly: 0, yearly: 0 };
  }

  private async getTopPerformingCoursesByRevenue(teacherId: string): Promise<{
    courseId: Types.ObjectId;
    revenue: number;
    enrollments: number;
  }[]> {
    // TODO: Implement actual top performing courses calculation
    return [];
  }

  private async getTotalStudentsCount(teacherId: string, courseId?: string): Promise<number> {
    try {
      const query: any = { creator: new Types.ObjectId(teacherId) };
      if (courseId) {
        query._id = new Types.ObjectId(courseId);
      }

      const courses = await Course.find(query);
      return courses.reduce((total, course) => total + (course.totalEnrollment || 0), 0);
    } catch (error) {
      Logger.error('❌ Failed to get total students count:', error);
      return 0;
    }
  }

  private getDateRange(period: string, startDate?: Date, endDate?: Date): DateRange {
    const now = new Date();
    
    if (startDate && endDate) {
      return { startDate, endDate };
    }

    switch (period) {
      case 'daily':
        return { startDate: startOfDay(now), endDate: endOfDay(now) };
      case 'weekly':
        return { startDate: startOfWeek(now), endDate: endOfWeek(now) };
      case 'monthly':
        return { startDate: startOfMonth(now), endDate: endOfMonth(now) };
      case 'yearly':
        return { startDate: startOfYear(now), endDate: endOfYear(now) };
      default:
        return { startDate: startOfMonth(now), endDate: endOfMonth(now) };
    }
  }

  private generateCacheKey(prefix: string, filters: AnalyticsFilters): string {
    const parts = [
      prefix,
      filters.teacherId,
      filters.courseId || 'all',
      filters.period,
      filters.startDate?.toISOString().split('T')[0] || 'current',
      filters.endDate?.toISOString().split('T')[0] || 'current',
    ];
    return parts.join(':');
  }

  private async generateInsights(
    courseAnalytics: ICourseAnalytics[],
    revenueAnalytics: IRevenueAnalytics | null,
    performanceMetrics: IPerformanceMetrics | null
  ): Promise<{ topInsight: string; recommendations: string[]; alerts: string[] }> {
    const recommendations: string[] = [];
    const alerts: string[] = [];
    let topInsight = 'Your courses are performing well overall.';

    // Analyze course performance
    if (courseAnalytics.length > 0) {
      const avgCompletionRate = courseAnalytics.reduce((sum, ca) => sum + ca.completionRate, 0) / courseAnalytics.length;
      
      if (avgCompletionRate < 50) {
        alerts.push('Low course completion rates detected');
        recommendations.push('Consider reviewing course structure and content engagement');
      }
    }

    // Analyze revenue trends
    if (revenueAnalytics && revenueAnalytics.totalRevenue > 0) {
      topInsight = `You've generated $${revenueAnalytics.totalRevenue.toFixed(2)} in total revenue.`;
    }

    return {
      topInsight,
      recommendations,
      alerts,
    };
  }
}

export default AnalyticsService;
